import React, { useEffect, useState, lazy, Suspense, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import Routes from './Routes';
import { initializeWebSocket, fetchAppData } from './redux/actions/appActions';
import EnhancedErrorBoundary from './components/common/EnhancedErrorBoundary';
import { getWebSocketUrl } from './config/env';
import { runApiEndpointCheck } from './utils/apiEndpointChecker';
import { checkBackendStatus, checkWebSocketStatus } from './utils/backendStatusChecker';
import NetworkStatusIndicator from './components/NetworkStatusIndicator';
import { useTheme } from './components/theme/ThemeManager';
import LoadingSpinner from './components/common/LoadingSpinner';
import DebugInfoPanel from './components/debug/DebugInfoPanel';
import usePerformanceMonitor from './hooks/usePerformanceMonitor';
import StructuredDataManager from './components/seo/StructuredDataManager';
import { initResourceOptimization } from './utils/resourceOptimizer';
import AccessibilityManager from './components/accessibility/AccessibilityManager';
import SkipLink from './components/accessibility/SkipLink';
import { initAccessibility } from './utils/accessibilityUtils';
import TestRunner from './components/testing/TestRunner';
import { AnalyticsProvider } from './components/analytics';
import './App.css';
import './styles/accessibility.css';
import './styles/theme.css';

function App() {
  const dispatch = useDispatch();
  const [initialized, setInitialized] = useState(false);

  // Initialize performance monitoring
  const performance = usePerformanceMonitor({
    enabled: process.env.NODE_ENV === 'development',
    autoMarkRenders: true,
    autoMarkEffects: true,
    autoMarkEvents: true,
    reportInterval: 30000,
    onReport: (report) => {
      console.debug('Performance Report:', report);

      // Log slow resource loads
      const slowResources = report.resources.filter(r => r.duration > 1000);
      if (slowResources.length > 0) {
        console.warn('Slow resource loads:', slowResources);
      }

      // Log performance errors
      if (report.errors.length > 0) {
        console.warn('Performance errors:', report.errors);
      }
    }
  });

  // Set component name for performance monitoring
  performance.setComponentName('App');

  // Get WebSocket connection status from Redux
  const websocketStatus = useSelector(state => state.websocket?.status);

  // State for API and backend status
  const [apiCheckResults, setApiCheckResults] = useState(null);
  const [backendStatus, setBackendStatus] = useState(null);
  const [wsStatus, setWsStatus] = useState(null);

  // Memoize the WebSocket configuration to prevent unnecessary re-renders
  const wsConfig = useMemo(() => ({
    autoReconnect: true,
    debug: process.env.REACT_APP_DEBUG === 'true' || true,
    reconnectInterval: 2000,
    maxReconnectAttempts: 5,
    heartbeatInterval: 30000,
    connectionTimeout: 5000,
    rateLimiting: {
      enabled: true,
      maxMessagesPerSecond: 20,
      burstSize: 50
    },
    compression: {
      enabled: true,
      threshold: 1024
    },
    batchingEnabled: true,
    batchInterval: 50,
    maxBatchSize: 20
  }), []);

  // Create a simplified initialization function for MVP
  const initializeApp = useCallback(async () => {
    try {
      // Mark the start of initialization
      performance.mark('init-start');
      console.log('Initializing App Builder MVP...');

      // Set a timeout to ensure we don't hang forever
      const initTimeout = setTimeout(() => {
        console.warn('Initialization taking too long, proceeding anyway...');
        setInitialized(true);
      }, 5000); // 5 second timeout

      // Quick backend status check with short timeout
      try {
        performance.mark('backend-check-start');
        const backendStatusResult = await Promise.race([
          checkBackendStatus({ timeout: 2000 }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Backend check timeout')), 2000))
        ]);
        setBackendStatus(backendStatusResult);
        performance.mark('backend-check-end');
        console.log('Backend status:', backendStatusResult.available ? 'Available' : 'Offline');
      } catch (error) {
        console.warn('Backend check failed, continuing in offline mode:', error.message);
        setBackendStatus({ available: false, error: error.message });
      }

      // Quick WebSocket check with short timeout
      try {
        performance.mark('ws-check-start');
        const wsStatusResult = await Promise.race([
          checkWebSocketStatus({ timeout: 2000 }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('WebSocket check timeout')), 2000))
        ]);
        setWsStatus(wsStatusResult);
        performance.mark('ws-check-end');
        console.log('WebSocket status:', wsStatusResult.available ? 'Available' : 'Offline');
      } catch (error) {
        console.warn('WebSocket check failed, continuing with HTTP fallback:', error.message);
        setWsStatus({ available: false, error: error.message });
      }

      // Quick API endpoint check
      try {
        performance.mark('api-check-start');
        const endpointResults = await Promise.race([
          runApiEndpointCheck(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('API check timeout')), 2000))
        ]);
        setApiCheckResults(endpointResults);
        performance.mark('api-check-end');
        console.log('API endpoints:', endpointResults.anyAvailable ? 'Available' : 'Offline');
      } catch (error) {
        console.warn('API check failed, continuing in offline mode:', error.message);
        setApiCheckResults({ anyAvailable: false, available: [], unavailable: [] });
      }

      // Try to initialize WebSocket in the background (non-blocking)
      try {
        const wsUrl = process.env.REACT_APP_WS_URL || getWebSocketUrl('app_builder');
        console.log('Attempting WebSocket connection to:', wsUrl);

        // Don't wait for WebSocket - initialize in background
        dispatch(initializeWebSocket({
          url: wsUrl,
          ...wsConfig,
          onSuccess: () => {
            console.log('WebSocket connected successfully');
            // Try to fetch data once connected
            dispatch(fetchAppData()).catch(err =>
              console.warn('Data fetch after WebSocket connection failed:', err)
            );
          },
          onError: (error) => {
            console.warn('WebSocket connection failed:', error);
          }
        }));
      } catch (wsError) {
        console.warn('WebSocket initialization error:', wsError);
      }

      // Try to fetch initial data (non-blocking)
      try {
        dispatch(fetchAppData()).catch(err =>
          console.warn('Initial data fetch failed:', err)
        );
      } catch (fetchError) {
        console.warn('Data fetch error:', fetchError);
      }

      // Clear the timeout since we're finishing
      clearTimeout(initTimeout);

      // Mark the end of initialization
      performance.mark('init-end');
      performance.measure('total-init-time', 'init-start', 'init-end');

      console.log('App Builder MVP initialized successfully');
      setInitialized(true);
    } catch (error) {
      console.error('Error initializing app:', error);
      // Always set initialized to true so the app loads
      setInitialized(true);
    }
  }, [dispatch, wsConfig, performance]);

  // Call the initialization function when the component mounts
  useEffect(() => {
    // Set a fallback timer to ensure the app loads no matter what
    const fallbackTimer = setTimeout(() => {
      console.warn('Fallback timer triggered - forcing app to load');
      setInitialized(true);
    }, 8000); // 8 second absolute maximum

    initializeApp().finally(() => {
      clearTimeout(fallbackTimer);
    });

    // Initialize resource optimization (non-blocking)
    try {
      initResourceOptimization();
    } catch (error) {
      console.warn('Resource optimization failed:', error);
    }

    // Initialize accessibility features (non-blocking)
    try {
      initAccessibility();
    } catch (error) {
      console.warn('Accessibility initialization failed:', error);
    }

    // Cleanup function
    return () => {
      clearTimeout(fallbackTimer);
    };
  }, [initializeApp]);

  if (!initialized) {
    return (
      <LoadingSpinner
        tip="Loading App Builder 201 MVP..."
        fullScreen={true}
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white'
        }}
      />
    );
  }

  // Display debug info in development mode - memoized to prevent unnecessary re-renders
  const showDebugInfo = useMemo(() => process.env.NODE_ENV === 'development', []);

  // State for showing test runner
  const [showTestRunner, setShowTestRunner] = useState(false);

  // Get theme information from context
  const theme = useTheme();
  const { colors, theme: themeMode } = theme || { colors: {}, theme: 'light' };

  // Get primary color for meta theme-color - memoized to prevent unnecessary re-renders
  const primaryColor = useMemo(() => colors?.primary || '#2563EB', [colors]);

  // Memoize the error handler to prevent unnecessary re-renders
  const handleError = useCallback((error, errorInfo) => {
    console.error('App Error:', error, errorInfo);
    // You could send this to a server-side error tracking service

    // Example of sending error to a tracking service
    if (process.env.REACT_APP_ERROR_TRACKING_URL) {
      try {
        fetch(process.env.REACT_APP_ERROR_TRACKING_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            error: error.toString(),
            errorInfo,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
          }),
        });
      } catch (e) {
        console.error('Failed to send error to tracking service:', e);
      }
    }
  }, []);

  // Structured data options
  const structuredDataOptions = useMemo(() => ({
    website: {
      name: 'App Builder 201',
      description: 'Build your application with minimal setup',
      url: window.location.origin
    },
    organization: {
      name: 'App Builder Team',
      url: window.location.origin,
      logo: `${window.location.origin}/logo512.png`
    },
    software: {
      name: 'App Builder 201',
      description: 'Build your application with minimal setup',
      applicationCategory: 'DeveloperApplication'
    }
  }), []);

  return (
    <EnhancedErrorBoundary
      onError={handleError}
    >
      {/* Meta tags and resource hints */}
      <Helmet>
        {/* Essential Meta Tags */}
        <meta name="theme-color" content={primaryColor} />
        <meta name="description" content="App Builder 201 - Build your application with minimal setup" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />

        {/* SEO Meta Tags */}
        <meta name="keywords" content="app builder, application development, low-code, no-code, development tool" />
        <meta name="author" content="App Builder Team" />
        <meta name="robots" content="index, follow" />

        {/* Open Graph / Social Media */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content="App Builder 201" />
        <meta property="og:description" content="Build your application with minimal setup" />
        <meta property="og:image" content="/logo512.png" />
        <meta property="og:url" content={window.location.href} />

        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="App Builder 201" />
        <meta name="twitter:description" content="Build your application with minimal setup" />
        <meta name="twitter:image" content="/logo512.png" />

        {/* Resource Hints */}
        <link rel="preconnect" href="https://backend:8000" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://backend:8000" />

        {/* Preload Critical Resources */}
        <link rel="preload" href="/app-builder" as="fetch" crossOrigin="anonymous" />
        <link rel="preload" href="/static/css/main.css" as="style" />
        <link rel="preload" href="/static/js/main.js" as="script" />

        {/* PWA Related */}
        <link rel="manifest" href="/manifest.json" />
        <link rel="apple-touch-icon" href="/logo192.png" />

        {/* Color Scheme */}
        <meta name="color-scheme" content={themeMode === 'dark' ? 'dark' : 'light'} />
      </Helmet>

      {/* Structured data for SEO */}
      <StructuredDataManager
        type="software"
        options={structuredDataOptions}
      />
      <Router>
        <AnalyticsProvider>
          {/* Skip link for keyboard navigation */}
          <SkipLink targetId="main-content" />

          {/* Network status indicator (always visible when offline) */}
          <NetworkStatusIndicator showOfflineOnly={true} />

          {showDebugInfo && (
            <DebugInfoPanel
              websocketStatus={websocketStatus}
              apiCheckResults={apiCheckResults}
              backendStatus={backendStatus}
              wsStatus={wsStatus}
              themeMode={themeMode}
            />
          )}

          {/* Use Suspense for code splitting */}
          <Suspense fallback={<LoadingSpinner tip="Loading routes..." fullScreen={true} />}>
            <main id="main-content">
              <Routes />
            </main>
          </Suspense>

          {/* Accessibility Manager */}
          <AccessibilityManager position="right" />

          {/* Test Runner */}
          <div className="test-runner-container">
            <button
              className="test-runner-toggle"
              onClick={() => setShowTestRunner(!showTestRunner)}
            >
              {showTestRunner ? 'Hide Tests' : 'Run Tests'}
            </button>

            {showTestRunner && (
              <TestRunner
                onComplete={(results) => {
                  console.log('Test results:', results);
                }}
              />
            )}
          </div>
        </AnalyticsProvider>
      </Router>

      <style>
        {`
          .test-runner-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
          }

          .test-runner-toggle {
            padding: 10px 20px;
            background-color: var(--color-primary, #1976d2);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: var(--shadow-md, 0 2px 8px rgba(0, 0, 0, 0.15));
          }

          .test-runner-toggle:hover {
            background-color: var(--color-primary-dark, #1565c0);
          }
        `}
      </style>
    </EnhancedErrorBoundary>
  );
}

export default App;


