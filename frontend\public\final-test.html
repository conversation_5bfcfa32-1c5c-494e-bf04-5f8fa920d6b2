<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final React Test - App Builder 201</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid;
            background: rgba(255, 255, 255, 0.1);
        }
        .success { border-color: #28a745; }
        .error { border-color: #dc3545; }
        .warning { border-color: #ffc107; }
        .info { border-color: #17a2b8; }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 8px;
            background: white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .results {
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 App Builder 201 - Final React Test</h1>
            <p>Comprehensive test to verify React application is working correctly</p>
        </div>

        <div id="test-status">
            <div class="status info">
                <span class="loading"></span> Initializing tests...
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎯 Quick Actions</h3>
                <button onclick="runFullTest()">🔄 Run Full Test</button>
                <button onclick="openMainApp()">🚀 Open Main App</button>
                <button onclick="testReactOnly()">⚛️ Test React Only</button>
                <button onclick="clearResults()">🗑️ Clear Results</button>
            </div>
            
            <div class="card">
                <h3>📊 Test Summary</h3>
                <div id="test-summary">
                    <div>React Global: <span id="react-status">⏳ Pending</span></div>
                    <div>ReactDOM: <span id="reactdom-status">⏳ Pending</span></div>
                    <div>Root Element: <span id="root-status">⏳ Pending</span></div>
                    <div>Bundle Loading: <span id="bundle-status">⏳ Pending</span></div>
                    <div>App Rendering: <span id="app-status">⏳ Pending</span></div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📱 Live App Preview</h3>
            <iframe id="app-frame" src="/" title="React App"></iframe>
        </div>

        <div class="card">
            <h3>📋 Test Results</h3>
            <div class="results" id="test-results">
                Click "Run Full Test" to start comprehensive testing...
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            testResults.push(`[${timestamp}] ${emoji} ${message}`);
            updateResults();
        }

        function updateResults() {
            document.getElementById('test-results').innerHTML = testResults.slice(-20).join('<br>');
        }

        function clearResults() {
            testResults = [];
            updateResults();
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('test-status');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            statusEl.innerHTML = `<div class="status ${className}">${message}</div>`;
        }

        function updateTestSummary(test, status) {
            const statusEl = document.getElementById(`${test}-status`);
            if (statusEl) {
                statusEl.innerHTML = status === 'pass' ? '✅ Pass' : status === 'fail' ? '❌ Fail' : '⏳ Pending';
            }
        }

        async function testReactGlobal() {
            log('Testing React global availability...');
            
            try {
                // Test if we can access the main app iframe
                const iframe = document.getElementById('app-frame');
                await new Promise((resolve) => {
                    iframe.onload = resolve;
                    if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
                        resolve();
                    }
                });

                // Wait a bit for React to initialize
                await new Promise(resolve => setTimeout(resolve, 3000));

                const iframeWindow = iframe.contentWindow;
                
                if (iframeWindow && iframeWindow.React) {
                    log(`React is available! Version: ${iframeWindow.React.version || 'Unknown'}`, 'success');
                    updateTestSummary('react', 'pass');
                    return true;
                } else {
                    log('React is not available in the main app', 'error');
                    updateTestSummary('react', 'fail');
                    return false;
                }
            } catch (error) {
                log(`Error testing React: ${error.message}`, 'error');
                updateTestSummary('react', 'fail');
                return false;
            }
        }

        async function testReactDOM() {
            log('Testing ReactDOM availability...');
            
            try {
                const iframe = document.getElementById('app-frame');
                const iframeWindow = iframe.contentWindow;
                
                if (iframeWindow && iframeWindow.ReactDOM) {
                    log('ReactDOM is available!', 'success');
                    updateTestSummary('reactdom', 'pass');
                    return true;
                } else {
                    log('ReactDOM is not available', 'error');
                    updateTestSummary('reactdom', 'fail');
                    return false;
                }
            } catch (error) {
                log(`Error testing ReactDOM: ${error.message}`, 'error');
                updateTestSummary('reactdom', 'fail');
                return false;
            }
        }

        async function testRootElement() {
            log('Testing root element...');
            
            try {
                const iframe = document.getElementById('app-frame');
                const iframeDoc = iframe.contentDocument;
                
                if (iframeDoc) {
                    const rootElement = iframeDoc.getElementById('root');
                    if (rootElement) {
                        if (rootElement.children.length > 0) {
                            log(`Root element found with ${rootElement.children.length} children`, 'success');
                            updateTestSummary('root', 'pass');
                            return true;
                        } else {
                            log('Root element found but empty', 'warning');
                            updateTestSummary('root', 'fail');
                            return false;
                        }
                    } else {
                        log('Root element not found', 'error');
                        updateTestSummary('root', 'fail');
                        return false;
                    }
                } else {
                    log('Cannot access iframe document', 'error');
                    updateTestSummary('root', 'fail');
                    return false;
                }
            } catch (error) {
                log(`Error testing root element: ${error.message}`, 'error');
                updateTestSummary('root', 'fail');
                return false;
            }
        }

        async function testBundleLoading() {
            log('Testing bundle loading...');
            
            try {
                const response = await fetch('/static/js/main.92165ac7.js');
                if (response.ok) {
                    const size = response.headers.get('content-length');
                    log(`Main bundle loaded successfully (${size ? Math.round(size/1024/1024*100)/100 + 'MB' : 'unknown size'})`, 'success');
                    updateTestSummary('bundle', 'pass');
                    return true;
                } else {
                    log(`Bundle loading failed: ${response.status}`, 'error');
                    updateTestSummary('bundle', 'fail');
                    return false;
                }
            } catch (error) {
                log(`Error testing bundle: ${error.message}`, 'error');
                updateTestSummary('bundle', 'fail');
                return false;
            }
        }

        async function testAppRendering() {
            log('Testing app rendering...');
            
            try {
                const iframe = document.getElementById('app-frame');
                const iframeDoc = iframe.contentDocument;
                
                if (iframeDoc) {
                    // Look for common React/Ant Design elements
                    const antElements = iframeDoc.querySelectorAll('[class*="ant-"]');
                    const reactElements = iframeDoc.querySelectorAll('[data-reactroot]');
                    
                    if (antElements.length > 0 || reactElements.length > 0) {
                        log(`App is rendering! Found ${antElements.length} Ant Design elements and ${reactElements.length} React elements`, 'success');
                        updateTestSummary('app', 'pass');
                        return true;
                    } else {
                        log('No React components detected in rendered app', 'warning');
                        updateTestSummary('app', 'fail');
                        return false;
                    }
                } else {
                    log('Cannot access app content', 'error');
                    updateTestSummary('app', 'fail');
                    return false;
                }
            } catch (error) {
                log(`Error testing app rendering: ${error.message}`, 'error');
                updateTestSummary('app', 'fail');
                return false;
            }
        }

        async function runFullTest() {
            updateStatus('🔄 Running comprehensive React tests...', 'info');
            clearResults();
            
            log('🚀 Starting comprehensive React application test...', 'info');
            log('=' .repeat(50), 'info');
            
            // Reset all test statuses
            ['react', 'reactdom', 'root', 'bundle', 'app'].forEach(test => {
                updateTestSummary(test, 'pending');
            });
            
            const tests = [
                { name: 'Bundle Loading', fn: testBundleLoading },
                { name: 'React Global', fn: testReactGlobal },
                { name: 'ReactDOM', fn: testReactDOM },
                { name: 'Root Element', fn: testRootElement },
                { name: 'App Rendering', fn: testAppRendering }
            ];
            
            let passedTests = 0;
            
            for (const test of tests) {
                log(`\nRunning ${test.name} test...`, 'info');
                try {
                    const result = await test.fn();
                    if (result) passedTests++;
                } catch (error) {
                    log(`Test ${test.name} failed with error: ${error.message}`, 'error');
                }
                
                // Wait a bit between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('\n' + '=' .repeat(50), 'info');
            log(`📊 Test Summary: ${passedTests}/${tests.length} tests passed`, passedTests === tests.length ? 'success' : 'warning');
            
            if (passedTests === tests.length) {
                updateStatus('🎉 All tests passed! React application is working correctly.', 'success');
            } else if (passedTests >= tests.length * 0.7) {
                updateStatus('⚠️ Most tests passed. React application is mostly working.', 'warning');
            } else {
                updateStatus('❌ Many tests failed. React application has issues.', 'error');
            }
        }

        async function testReactOnly() {
            updateStatus('🔄 Testing React availability only...', 'info');
            clearResults();
            
            const result = await testReactGlobal();
            if (result) {
                updateStatus('✅ React is working!', 'success');
            } else {
                updateStatus('❌ React test failed', 'error');
            }
        }

        function openMainApp() {
            window.open('/', '_blank');
        }

        // Auto-run test when page loads
        window.addEventListener('load', () => {
            setTimeout(runFullTest, 2000);
        });
    </script>
</body>
</html>
